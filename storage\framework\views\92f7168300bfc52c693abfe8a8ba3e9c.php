<!DOCTYPE html>
<html lang="en">
<?php use Illuminate\Support\Facades\Auth; ?> 

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta name="csrf-token" content="<?php echo e(csrf_token()); ?>">
    
    <title>Персонаж: <?php echo e(Auth::check() ? Auth::user()->name : 'Гость'); ?></title>

    
    <?php echo app('Illuminate\Foundation\Vite')(['resources/css/app.css', 'resources/js/app.js']); ?>

    
    <style>
        /* Стилизация скроллбара для Webkit (Chrome, Safari, новые версии Edge) */
        ::-webkit-scrollbar {
            width: 8px;
            height: 8px;
        }

        ::-webkit-scrollbar-track {
            background: #1a1814;
            border-radius: 4px;
        }

        ::-webkit-scrollbar-thumb {
            background: #514b3c;
            border-radius: 4px;
        }

        ::-webkit-scrollbar-thumb:hover {
            background: #8c784e;
        }

        /* Стилизация скроллбара для Firefox */
        * {
            scrollbar-width: thin;
            scrollbar-color: #514b3c #1a1814;
        }

        /* Специальные стили для контейнеров с прокруткой */
        .potion-list,
        .max-h-32,
        .max-h-24 {
            scrollbar-width: thin;
            scrollbar-color: #514b3c #1a1814;
        }

        /* Анимации для зелий */
        @keyframes fadeIn {
            from {
                opacity: 0;
            }

            to {
                opacity: 1;
            }
        }

        @keyframes pulse {
            0% {
                transform: scale(1);
            }

            50% {
                transform: scale(1.05);
            }

            100% {
                transform: scale(1);
            }
        }

        .animate-fade-in {
            animation: fadeIn 0.5s ease-in-out;
        }

        .animate-pulse {
            animation: pulse 2s infinite ease-in-out;
        }

        /* Стили для свечения зелий разных цветов */
        .glow-gold {
            box-shadow: 0 0 10px 2px rgba(229, 183, 105, 0.5);
        }

        .glow-green {
            box-shadow: 0 0 10px 2px rgba(166, 195, 123, 0.5);
        }

        .glow-blue {
            box-shadow: 0 0 10px 2px rgba(100, 149, 237, 0.5);
        }

        .glow-purple {
            box-shadow: 0 0 10px 2px rgba(147, 112, 219, 0.5);
        }

        .glow-red {
            box-shadow: 0 0 10px 2px rgba(231, 124, 124, 0.5);
        }
    </style>
</head>



<body class="bg-[#2f2d2b] text-[#f5f5f5] font-serif flex flex-col min-h-screen">
    
    <div class="container max-w-md mx-auto px-1 py-0 border-2 border-[#a6925e] rounded-lg flex-grow">

        
        <?php if (isset($component)) { $__componentOriginalee64098a97531effaa5ca39da6b3f2bd = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginalee64098a97531effaa5ca39da6b3f2bd = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'components.layout.hp-mp-bar','data' => ['actualResources' => $actualResources,'userProfile' => $userProfile]] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? $attributes->all() : [])); ?>
<?php $component->withName('layout.hp-mp-bar'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag): ?>
<?php $attributes = $attributes->except(\Illuminate\View\AnonymousComponent::ignoredParameterNames()); ?>
<?php endif; ?>
<?php $component->withAttributes(['actualResources' => \Illuminate\View\Compilers\BladeCompiler::sanitizeComponentAttribute($actualResources),'userProfile' => \Illuminate\View\Compilers\BladeCompiler::sanitizeComponentAttribute($userProfile)]); ?>
            
            <?php if (isset($component)) { $__componentOriginale4d3e407db3a46bc862a5907d9d4d4eb = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginale4d3e407db3a46bc862a5907d9d4d4eb = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'components.layout.notifications-bar','data' => ['hasUnreadMessages' => $hasUnreadMessages ?? false,'unreadMessagesCount' => $unreadMessagesCount ?? 0,'hasBrokenItems' => $hasBrokenItems ?? false,'brokenItemsCount' => $brokenItemsCount ?? 0]] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? $attributes->all() : [])); ?>
<?php $component->withName('layout.notifications-bar'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag): ?>
<?php $attributes = $attributes->except(\Illuminate\View\AnonymousComponent::ignoredParameterNames()); ?>
<?php endif; ?>
<?php $component->withAttributes(['hasUnreadMessages' => \Illuminate\View\Compilers\BladeCompiler::sanitizeComponentAttribute($hasUnreadMessages ?? false),'unreadMessagesCount' => \Illuminate\View\Compilers\BladeCompiler::sanitizeComponentAttribute($unreadMessagesCount ?? 0),'hasBrokenItems' => \Illuminate\View\Compilers\BladeCompiler::sanitizeComponentAttribute($hasBrokenItems ?? false),'brokenItemsCount' => \Illuminate\View\Compilers\BladeCompiler::sanitizeComponentAttribute($brokenItemsCount ?? 0)]); ?>
<?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginale4d3e407db3a46bc862a5907d9d4d4eb)): ?>
<?php $attributes = $__attributesOriginale4d3e407db3a46bc862a5907d9d4d4eb; ?>
<?php unset($__attributesOriginale4d3e407db3a46bc862a5907d9d4d4eb); ?>
<?php endif; ?>
<?php if (isset($__componentOriginale4d3e407db3a46bc862a5907d9d4d4eb)): ?>
<?php $component = $__componentOriginale4d3e407db3a46bc862a5907d9d4d4eb; ?>
<?php unset($__componentOriginale4d3e407db3a46bc862a5907d9d4d4eb); ?>
<?php endif; ?>
         <?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginalee64098a97531effaa5ca39da6b3f2bd)): ?>
<?php $attributes = $__attributesOriginalee64098a97531effaa5ca39da6b3f2bd; ?>
<?php unset($__attributesOriginalee64098a97531effaa5ca39da6b3f2bd); ?>
<?php endif; ?>
<?php if (isset($__componentOriginalee64098a97531effaa5ca39da6b3f2bd)): ?>
<?php $component = $__componentOriginalee64098a97531effaa5ca39da6b3f2bd; ?>
<?php unset($__componentOriginalee64098a97531effaa5ca39da6b3f2bd); ?>
<?php endif; ?>

        
        <?php if (isset($component)) { $__componentOriginal85b5c6510a4667d62a225031f53f7a0d = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginal85b5c6510a4667d62a225031f53f7a0d = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'components.layout.currency-display','data' => ['userProfile' => $userProfile,'experienceProgress' => $experienceProgress ?? null]] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? $attributes->all() : [])); ?>
<?php $component->withName('layout.currency-display'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag): ?>
<?php $attributes = $attributes->except(\Illuminate\View\AnonymousComponent::ignoredParameterNames()); ?>
<?php endif; ?>
<?php $component->withAttributes(['userProfile' => \Illuminate\View\Compilers\BladeCompiler::sanitizeComponentAttribute($userProfile),'experienceProgress' => \Illuminate\View\Compilers\BladeCompiler::sanitizeComponentAttribute($experienceProgress ?? null)]); ?>
<?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginal85b5c6510a4667d62a225031f53f7a0d)): ?>
<?php $attributes = $__attributesOriginal85b5c6510a4667d62a225031f53f7a0d; ?>
<?php unset($__attributesOriginal85b5c6510a4667d62a225031f53f7a0d); ?>
<?php endif; ?>
<?php if (isset($__componentOriginal85b5c6510a4667d62a225031f53f7a0d)): ?>
<?php $component = $__componentOriginal85b5c6510a4667d62a225031f53f7a0d; ?>
<?php unset($__componentOriginal85b5c6510a4667d62a225031f53f7a0d); ?>
<?php endif; ?>

        
        <div class="">
            
            <?php if (isset($component)) { $__componentOriginal360d002b1b676b6f84d43220f22129e2 = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginal360d002b1b676b6f84d43220f22129e2 = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'components.breadcrumbs','data' => ['breadcrumbs' => $breadcrumbs]] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? $attributes->all() : [])); ?>
<?php $component->withName('breadcrumbs'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag): ?>
<?php $attributes = $attributes->except(\Illuminate\View\AnonymousComponent::ignoredParameterNames()); ?>
<?php endif; ?>
<?php $component->withAttributes(['breadcrumbs' => \Illuminate\View\Compilers\BladeCompiler::sanitizeComponentAttribute($breadcrumbs)]); ?>
<?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginal360d002b1b676b6f84d43220f22129e2)): ?>
<?php $attributes = $__attributesOriginal360d002b1b676b6f84d43220f22129e2; ?>
<?php unset($__attributesOriginal360d002b1b676b6f84d43220f22129e2); ?>
<?php endif; ?>
<?php if (isset($__componentOriginal360d002b1b676b6f84d43220f22129e2)): ?>
<?php $component = $__componentOriginal360d002b1b676b6f84d43220f22129e2; ?>
<?php unset($__componentOriginal360d002b1b676b6f84d43220f22129e2); ?>
<?php endif; ?>

            
            <?php if(isset($canReturnToBattle) && $canReturnToBattle && isset($lastBattleRoute) && $lastBattleRoute): ?>
                
                <a href="<?php echo e(url($lastBattleRoute)); ?>" 
                    class="block w-full mb-2 max-w-xs mx-auto px-2 py-2 mt-3 text-lg font-semibold text-[#c4a46b]
                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                  bg-gradient-to-b from-[#2b2a21] to-[#1d1c17] border border-[#8c784e]
                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                  rounded-md uppercase tracking-wide text-center
                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                  hover:border-[#b59e70] hover:text-[#dfc590] hover:shadow-md
                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                  transition-all duration-300 magic-glow">
                    

                    ⚔️ ВЕРНУТЬСЯ В БОЙ ⚔️
                </a>
            <?php endif; ?>

            
            
            <p
                class="block text-center text-white py-1.5 px-8 rounded-md shadow-lg bg-gradient-to-b from-[#4a4a3d] to-[#3b3a33] text-lg font-semibold">
                
                Алхимическая лаборатория
            </p>
        </div>

        
        <div class="bg-[#211f1a] px-2.5 py-3 shadow-inner"> 

            
            <div class="mb-4 text-sm text-[#d3c6a6] border-l-2 border-[#8c784e] pl-2">
                <p>Добро пожаловать в алхимическую лабораторию! Здесь вы можете создавать магические зелья и эликсиры,
                    которые помогут вам в приключениях.</p>
            </div>

            
            <div class="mb-4 text-center">
                <a href="<?php echo e(route('masters.alchemist.enhancement')); ?>"
                    class="inline-block px-4 py-2 bg-gradient-to-b from-[#4a452c] to-[#2a2721] text-[#e5b769] border border-[#8c784e] rounded-md hover:from-[#5a552c] hover:to-[#3a3721] hover:text-[#f0d89e] hover:border-[#a6925e] transition-all duration-300">
                    <span class="flex items-center">
                        <span class="mr-2">🧪</span>
                        <span>Улучшение зелий</span>
                    </span>
                </a>
            </div>

            
            <div class="mb-4">
                <h3 class="text-[#e5b769] font-semibold text-base mb-2 border-b border-[#3d3a2e] pb-1">Рабочий стол
                    алхимика</h3>

                
                <div class="grid grid-cols-1 md:grid-cols-2 gap-3 mb-3">
                    
                    <div id="active-brewing"
                        class="bg-gradient-to-b from-[#2a2722] to-[#1d1916] border border-[#514b3c] rounded-md p-2"
                        data-active="<?php echo e(isset($activeBrewing) || (isset($completedPotions) && count($completedPotions) > 0) ? 'true' : 'false'); ?>"
                        data-completed="<?php echo e((isset($completedPotions) && count($completedPotions) > 0) ? 'true' : 'false'); ?>">
                        <div class="flex items-center justify-between mb-2">
                            <span class="text-[#d3c6a6] text-sm font-medium">Котёл</span>
                            <?php if(isset($activeBrewing)): ?>
                                <span
                                    class="text-[#7cfc00] text-xs px-2 py-0.5 bg-[#1a301a] rounded border border-[#397239]">
                                    Варится
                                </span>
                            <?php elseif(isset($completedPotions) && count($completedPotions) > 0): ?>
                                <span
                                    class="text-[#a6e269] text-xs px-2 py-0.5 bg-[#1a301a] rounded border border-[#397239]">
                                    Готово
                                </span>
                            <?php else: ?>
                                <span
                                    class="text-[#e77c7c] text-xs px-2 py-0.5 bg-[#301a1a] rounded border border-[#562e2e]">
                                    Пуст
                                </span>
                            <?php endif; ?>
                        </div>

                        
                        <?php if(!isset($activeBrewing)): ?>
                            
                            <?php if(isset($completedPotions) && count($completedPotions) > 0): ?>
                                
                                <?php
                                    $firstCompletedPotion = reset($completedPotions);
                                    $brewingId = key($completedPotions);

                                    // Определяем цвет свечения в зависимости от качества зелья
                                    $qualityColor = 'green';
                                    switch ($firstCompletedPotion->quality) {
                                        case 'Обычное':
                                            $qualityColor = 'green';
                                            break;
                                        case 'Необычное':
                                            $qualityColor = 'blue';
                                            break;
                                        case 'Редкое':
                                            $qualityColor = 'purple';
                                            break;
                                        case 'Эпическое':
                                            $qualityColor = 'red';
                                            break;
                                        case 'Легендарное':
                                            $qualityColor = 'gold';
                                            break;
                                    }
                                ?>

                                <div class="flex flex-col">
                                    <div class="flex items-center mb-2">
                                        <div
                                            class="w-10 h-10 bg-[#1a1814] rounded border border-[#514b3c] flex items-center justify-center mr-2 glow-<?php echo e($qualityColor); ?>">
                                            <?php
                                                // Добавляем отладочную информацию
                                                $iconPath = $firstCompletedPotion->icon_path;
                                                $iconValue = $firstCompletedPotion->icon;

                                                // Проверяем, что путь к иконке корректный
                                                if (empty($iconPath) || !filter_var($iconPath, FILTER_VALIDATE_URL)) {
                                                    // Если путь некорректный, формируем его вручную
                                                    if (empty($iconValue)) {
                                                        $iconPath = asset('assets/potions/smallBottleHP.png');
                                                    } else {
                                                        // Если путь не содержит 'assets/', добавляем его
                                                        if (strpos($iconValue, 'assets/') === 0) {
                                                            $iconPath = asset($iconValue);
                                                        } else if (strpos($iconValue, 'potions/') === 0) {
                                                            $iconPath = asset("assets/{$iconValue}");
                                                        } else {
                                                            $iconPath = asset("assets/potions/{$iconValue}");
                                                        }
                                                    }
                                                }
                                            ?>
                                            <img src="<?php echo e($iconPath); ?>" alt="<?php echo e($firstCompletedPotion->name); ?>"
                                                class="w-8 h-8 animate-pulse">
                                        </div>
                                        <div class="flex-1">
                                            <div class="text-[#a6e269] text-xs font-medium"><?php echo e($firstCompletedPotion->name); ?>

                                                (готово)</div>
                                            <div class="text-[#9a9483] text-[10px]"><?php echo e($firstCompletedPotion->description); ?>

                                            </div>
                                        </div>
                                    </div>

                                    
                                    <div class="w-full bg-[#211f1a] h-2 rounded-full overflow-hidden mt-1">
                                        <div class="bg-gradient-to-r from-[#4a5a2c] to-[#a6c37b] h-full rounded-full"
                                            style="width: 100%"></div>
                                    </div>

                                    <div class="text-right text-[#9a9483] text-xs mt-1">
                                        Готово: <?php echo e($firstCompletedPotion->completed_at); ?>

                                    </div>



                                    
                                    <form action="<?php echo e(route('masters.alchemist.collectPotionRedirect')); ?>" method="POST"
                                        class="mt-2">
                                        <?php echo csrf_field(); ?>
                                        <input type="hidden" name="brewing_id" value="<?php echo e($brewingId); ?>">
                                        <button type="submit"
                                            class="w-full text-xs px-2 py-1.5 bg-gradient-to-b from-[#4a5a2c] to-[#334024] text-[#a6e269] border border-[#778c4e] rounded-sm hover:from-[#5a6a36] hover:to-[#3f4c2c] transition-colors duration-300">
                                            Собрать готовое зелье
                                        </button>
                                    </form>
                                </div>
                            <?php else: ?>
                                <div class="text-center py-6 opacity-80">
                                    <div class="text-[#9a9483] text-sm mb-1">Котел пуст и готов к работе</div>
                                    <div class="text-[#d3c6a6] text-xs">Выберите рецепт из рюкзака и поместите его на стол</div>
                                </div>
                            <?php endif; ?>
                        <?php else: ?>
                            
                            <div class="flex flex-col">
                                <div class="flex items-center mb-2">
                                    <div
                                        class="w-10 h-10 bg-[#1a1814] rounded border border-[#514b3c] flex items-center justify-center mr-2">
                                        <img src="<?php echo e($activeBrewing->icon_path); ?>" alt="<?php echo e($activeBrewing->potion_name); ?>"
                                            class="w-8 h-8">
                                    </div>
                                    <div class="flex-1">
                                        <div class="text-[#d3c6a6] text-xs font-medium"><?php echo e($activeBrewing->potion_name); ?>

                                        </div>
                                        <div class="text-[#9a9483] text-[10px]"><?php echo e($activeBrewing->description); ?></div>
                                    </div>
                                </div>

                                
                                <div class="w-full bg-[#211f1a] h-2 rounded-full overflow-hidden mt-1">
                                    <div class="bg-gradient-to-r from-[#4a452c] to-[#8c784e] h-full rounded-full transition-all duration-300"
                                        style="width: <?php echo e($brewingProgress); ?>%"></div>
                                </div>

                                
                                <div id="brewing-time-left" class="text-right text-[#9a9483] text-xs mt-1">
                                    Осталось: <?php echo e($timeLeftFormatted); ?>

                                </div>

                                
                                <?php if(isset($activeBrewing->completed) && $activeBrewing->completed): ?>
                                    
                                    <form action="<?php echo e(route('masters.alchemist.collectPotionRedirect')); ?>" method="POST"
                                        class="mt-2">
                                        <?php echo csrf_field(); ?>
                                        <input type="hidden" name="brewing_id"
                                            value="<?php echo e($activeBrewing->unique_id ?? $activeBrewing->id); ?>">
                                        <button type="submit"
                                            class="w-full text-xs px-2 py-1.5 bg-gradient-to-b from-[#4a5a2c] to-[#334024] text-[#a6e269] border border-[#778c4e] rounded-sm hover:from-[#5a6a36] hover:to-[#3f4c2c] transition-colors duration-300">
                                            Собрать готовое зелье
                                        </button>
                                    </form>
                                <?php else: ?>
                                    <button onclick="cancelBrewing()"
                                        class="mt-2 w-full text-xs px-2 py-1.5 bg-gradient-to-b from-[#3d3a33] to-[#252520] text-[#9a9483] border border-[#514b3c] rounded-sm hover:text-[#d3c6a6] hover:border-[#8c784e] transition-colors duration-300">
                                        Отменить варку
                                    </button>
                                <?php endif; ?>
                            </div>
                        <?php endif; ?>
                    </div>

                    
                    <div id="recipe-slot"
                        class="bg-gradient-to-b from-[#2a2722] to-[#1d1916] border border-[#514b3c] rounded-md p-2">
                        <div class="flex items-center justify-between mb-2">
                            <span class="text-[#d3c6a6] text-sm font-medium">Рецепт</span>
                            <span id="recipe-slot-status"
                                class="text-[#e77c7c] text-xs px-2 py-0.5 bg-[#301a1a] rounded border border-[#562e2e]">
                                Не выбран
                            </span>
                        </div>

                        <div id="recipe-slot-content" class="min-h-[120px]">
                            
                            <div id="empty-recipe-slot" class="text-center py-6 opacity-80">
                                <div class="text-[#9a9483] text-sm mb-1">Выберите рецепт из рюкзака</div>
                                <div class="text-[#d3c6a6] text-xs">Нажмите на рецепт, чтобы поместить его на стол</div>
                            </div>

                            
                            <div id="selected-recipe" class="hidden">
                                
                            </div>

                            
                            <div id="recipe-loading" class="hidden text-center py-6">
                                <div
                                    class="inline-block animate-spin rounded-full h-8 w-8 border-t-2 border-b-2 border-[#e5b769]">
                                </div>
                                <div class="text-[#d3c6a6] text-xs mt-2">Проверка ингредиентов...</div>
                            </div>
                        </div>
                    </div>
                </div>

                
                <div class="bg-gradient-to-b from-[#2a2722] to-[#1d1916] border border-[#514b3c] rounded-md p-2">
                    <div class="flex justify-between items-center mb-2">
                        <h4 class="text-[#d3c6a6] text-sm font-medium">Рецепты в рюкзаке</h4>
                        <span class="text-[#9a9483] text-xs">Уровень алхимии: <?php echo e($alchemyLevel ?? 1); ?></span>
                    </div>

                    <div class="space-y-2 max-h-64 overflow-y-auto pr-2 potion-list custom-scrollbar">
                        <?php if(isset($userRecipes) && $userRecipes->count() > 0): ?>
                            <?php $__currentLoopData = $userRecipes; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $userRecipe): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                <div class="bg-[#211f1a] rounded border border-[#514b3c] p-2 relative recipe-card hover:border-[#e5b769] transition-colors cursor-pointer"
                                    data-recipe-id="<?php echo e($userRecipe->recipe->id); ?>">
                                    <div class="flex items-center">
                                        <div
                                            class="w-10 h-10 bg-[#1a1814] rounded border border-[#514b3c] flex items-center justify-center mr-2 relative">
                                            <img src="<?php echo e($userRecipe->icon_path); ?>" alt="<?php echo e($userRecipe->recipe->name); ?>"
                                                class="w-8 h-8">
                                            <div
                                                class="absolute -bottom-1 -right-1 bg-[#211f1a] border border-[#e5b769] rounded-full px-1 text-[10px] text-[#e5b769] font-medium">
                                                x<?php echo e($userRecipe->quantity); ?>

                                            </div>
                                        </div>
                                        <div class="flex-1">
                                            <div class="text-[#d3c6a6] text-xs font-medium"><?php echo e($userRecipe->recipe->name); ?>

                                            </div>
                                            <div class="text-[#9a9483] text-[10px]"><?php echo e($userRecipe->recipe->effect); ?>

                                                <?php echo e($userRecipe->recipe->effect_value); ?>

                                            </div>
                                            <div class="text-[#9a9483] text-[10px]">Уровень:
                                                <?php echo e($userRecipe->recipe->min_alchemy_level); ?>

                                            </div>
                                        </div>
                                    </div>

                                    
                                    <div class="mt-2 text-center">
                                        <button type="button"
                                            class="text-xs px-3 py-1 bg-gradient-to-b from-[#3d3a33] to-[#252520] text-[#9a9483] border border-[#514b3c] rounded-sm hover:text-[#d3c6a6] hover:border-[#8c784e] transition-colors duration-300 recipe-details-btn"
                                            onclick="toggleRecipeDetails(event, '<?php echo e($userRecipe->recipe->id); ?>')">
                                            Подробнее
                                        </button>
                                    </div>

                                    
                                    <div class="recipe-details hidden mt-3" id="recipe-details-<?php echo e($userRecipe->recipe->id); ?>">
                                        
                                        <div class="mb-3 bg-[#1a1814] p-2 rounded">
                                            <div class="text-[#d3c6a6] text-xs font-medium mb-2 border-b border-[#3d3a2e] pb-1">
                                                Требуемые ингредиенты:</div>
                                            <div class="max-h-48 overflow-y-auto">
                                                <div class="grid grid-cols-2 gap-3">
                                                    <?php $__currentLoopData = $userRecipe->recipe->ingredients; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $ingredient): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                                        <?php
                                                            // Найти ресурс в инвентаре
                                                            // Получаем ID ресурса из ингредиента
                                                            $resourceId = $ingredient->resource_id;
                                                            $ingredientId = $ingredient->id;

                                                            // Ищем ресурс в инвентаре пользователя
                                                            $userResource = $inventoryResources->first(function ($item) use ($resourceId) {
                                                                return $item->resource->id === $resourceId;
                                                            });
                                                            $resourceQuantity = $userResource ? $userResource->total_quantity : 0;

                                                            // Ищем алхимический ингредиент у пользователя
                                                            $userAlchemyIngredient = Auth::user()->alchemyIngredients()
                                                                ->where('alchemy_ingredient_id', $ingredientId)
                                                                ->where('location', 'inventory')
                                                                ->where('quantity', '>', 0)
                                                                ->sum('quantity');

                                                            // Суммируем количество ресурсов и алхимических ингредиентов
                                                            $userQuantity = $resourceQuantity + $userAlchemyIngredient;
                                                            $neededQuantity = $ingredient->pivot->quantity;
                                                            $hasEnough = $userQuantity >= $neededQuantity;
                                                            $percentAvailable = $neededQuantity > 0 ? min(100, ($userQuantity / $neededQuantity) * 100) : 0;
                                                        ?>
                                                        <div
                                                            class="ingredient-card bg-[#211f1a] rounded border border-[#514b3c] p-3 hover:border-[#8c784e] transition-colors text-center">
                                                            
                                                            <div class="flex justify-center mb-2">
                                                                <div
                                                                    class="ingredient-icon-container w-12 h-12 bg-[#1a1814] rounded border border-[#514b3c] flex items-center justify-center">
                                                                    <img src="<?php echo e($ingredient->icon_path ?? asset('assets/icons/resources/default.png')); ?>"
                                                                        alt="<?php echo e($ingredient->name); ?>"
                                                                        class="w-10 h-10 <?php echo e(!$hasEnough ? 'opacity-70' : ''); ?>">
                                                                </div>
                                                            </div>
                                                            
                                                            <div class="ingredient-quantity text-xs mb-2">
                                                                <span
                                                                    class="<?php echo e($hasEnough ? 'text-[#a6c37b]' : 'text-[#e77c7c]'); ?> font-medium">
                                                                    <?php echo e($userQuantity); ?>/<?php echo e($neededQuantity); ?>

                                                                </span>
                                                            </div>
                                                            
                                                            <div
                                                                class="ingredient-progress w-full bg-[#1a1814] h-2 rounded-full overflow-hidden">
                                                                <div class="<?php echo e($hasEnough ? 'bg-gradient-to-r from-[#4a5a2c] to-[#a6c37b]' : 'bg-gradient-to-r from-[#5a2c2c] to-[#e77c7c]'); ?> h-full rounded-full"
                                                                    style="width: <?php echo e($percentAvailable); ?>%"></div>
                                                            </div>
                                                        </div>
                                                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                                </div>
                                            </div>

                                            
                                            <div class="flex justify-between items-center text-[10px] mb-2">
                                                <span class="text-[#9a9483]">Время приготовления:</span>
                                                <span
                                                    class="text-[#d3c6a6]"><?php echo e($userRecipe->recipe->brewing_time_formatted); ?></span>
                                            </div>

                                            
                                            <?php
                                                $canBrew = true;
                                                $cantBrewReason = '';

                                                // Проверка уровня алхимии
                                                if (($alchemyLevel ?? 1) < $userRecipe->recipe->min_alchemy_level) {
                                                    $canBrew = false;
                                                    $cantBrewReason = 'Требуется уровень алхимии: ' . $userRecipe->recipe->min_alchemy_level;
                                                }

                                                // Проверка ингредиентов
                                                $missingIngredients = [];
                                                foreach ($userRecipe->recipe->ingredients as $ingredient) {
                                                    // Получаем ID ресурса из ингредиента
                                                    $resourceId = $ingredient->resource_id;
                                                    $ingredientId = $ingredient->id;

                                                    // Ищем ресурс в инвентаре пользователя
                                                    $userResource = $inventoryResources->first(function ($item) use ($resourceId) {
                                                        return $item->resource->id === $resourceId;
                                                    });
                                                    $resourceQuantity = $userResource ? $userResource->total_quantity : 0;

                                                    // Ищем алхимический ингредиент у пользователя
                                                    $userAlchemyIngredient = Auth::user()->alchemyIngredients()
                                                        ->where('alchemy_ingredient_id', $ingredientId)
                                                        ->where('location', 'inventory')
                                                        ->where('quantity', '>', 0)
                                                        ->sum('quantity');

                                                    // Суммируем количество ресурсов и алхимических ингредиентов
                                                    $userQuantity = $resourceQuantity + $userAlchemyIngredient;
                                                    $neededQuantity = $ingredient->pivot->quantity;

                                                    if ($userQuantity < $neededQuantity) {
                                                        $canBrew = false;
                                                        $missingIngredients[] = $ingredient->name . ' (' . ($neededQuantity - $userQuantity) . ' шт.)';
                                                    }
                                                }

                                                if (!empty($missingIngredients)) {
                                                    $cantBrewReason = 'Не хватает: ' . implode(', ', $missingIngredients);
                                                }

                                                // Проверка активной варки
                                                if (isset($activeBrewing)) {
                                                    $canBrew = false;
                                                    $cantBrewReason = 'Алхимический стол занят';
                                                }
                                            ?>

                                            <?php if(($alchemyLevel ?? 1) < $userRecipe->recipe->min_alchemy_level): ?>
                                                <div class="text-[#e77c7c] text-[10px] mt-1 text-center">
                                                    Требуется уровень алхимии: <?php echo e($userRecipe->recipe->min_alchemy_level); ?>

                                                </div>
                                            <?php endif; ?>
                                        </div>

                                    </div>
                            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                        <?php else: ?>
                                <div class="text-center py-4 text-[#9a9483] text-sm">
                                    Нет доступных рецептов. Вы можете купить рецепты в магазине или найти их во время
                                    приключений.
                                </div>
                            <?php endif; ?>
                        </div>
                    </div>
                </div>

                
                <div class="mb-4">
                    <h3 class="text-[#e5b769] font-semibold text-base mb-2 border-b border-[#3d3a2e] pb-1">Улучшение
                        зелий
                    </h3>

                    <div class="bg-gradient-to-b from-[#2a2722] to-[#1d1916] border border-[#514b3c] rounded-md p-2">
                        <div class="text-[#d3c6a6] text-xs mb-2">
                            Выберите зелье и катализатор для улучшения качества и эффективности
                        </div>

                        <div class="flex space-x-2 mb-2">
                            
                            <div class="flex flex-col sm:flex-row w-full space-y-2 sm:space-y-0 sm:space-x-2">
                                
                                <div class="flex-1">
                                    <div class="text-[#9a9483] text-xs mb-1">Зелье для улучшения</div>
                                    <div id="enhancement-potion-slot"
                                        class="h-16 bg-[#211f1a] border border-[#514b3c] rounded flex items-center justify-center relative">
                                        <?php if(isset($userPotions) && $userPotions->count() > 0): ?>
                                            <div class="w-full h-full flex items-center justify-center cursor-pointer"
                                                onclick="togglePotionDropdown()">
                                                <div id="selected-potion-display"
                                                    class="text-[#9a9483] text-[10px] text-center px-2">
                                                    Выберите зелье
                                                </div>
                                            </div>
                                            <div id="potion-dropdown"
                                                class="hidden absolute left-0 right-0 top-full mt-1 bg-[#211f1a] border border-[#514b3c] rounded-sm z-20 max-h-32 overflow-y-auto">
                                                <?php $__currentLoopData = $userPotions; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $potion): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                                    <div onclick="selectPotionForEnhancement(<?php echo e($potion->id); ?>, '<?php echo e($potion->name); ?>', '<?php echo e($potion->quality); ?>', '<?php echo e(asset($potion->icon ?? 'assets/potions/default.png')); ?>')"
                                                        class="flex items-center p-1 hover:bg-[#2a2722] cursor-pointer border-b border-[#3d3a2e] last:border-b-0">
                                                        <img src="<?php echo e(asset($potion->icon ?? 'assets/potions/default.png')); ?>"
                                                            alt="<?php echo e($potion->name); ?>" class="w-6 h-6 mr-1">
                                                        <div class="flex flex-col">
                                                            <span class="text-[10px] text-[#d3c6a6]"><?php echo e($potion->name); ?></span>
                                                            <span class="text-[8px] text-[#9a9483]">Качество:
                                                                <?php echo e($potion->quality); ?></span>
                                                        </div>
                                                    </div>
                                                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                            </div>
                                        <?php else: ?>
                                            <div class="text-[#9a9483] text-[10px] text-center px-2">
                                                Нет доступных зелий
                                            </div>
                                        <?php endif; ?>
                                    </div>
                                </div>

                                
                                <div class="flex items-center justify-center">
                                    <div class="text-[#8c784e] text-xl">+</div>
                                </div>

                                
                                <div class="flex-1">
                                    <div class="text-[#9a9483] text-xs mb-1">Катализатор</div>
                                    <div id="enhancement-catalyst-slot"
                                        class="h-16 bg-[#211f1a] border border-[#514b3c] rounded flex items-center justify-center relative">
                                        <?php if(isset($userCatalysts) && count($userCatalysts) > 0): ?>
                                            <div class="w-full h-full flex items-center justify-center cursor-pointer"
                                                onclick="toggleCatalystDropdownForEnhancement()">
                                                <div id="selected-catalyst-display"
                                                    class="text-[#9a9483] text-[10px] text-center px-2">
                                                    Выберите катализатор
                                                </div>
                                            </div>
                                            <div id="catalyst-dropdown-enhancement"
                                                class="hidden absolute left-0 right-0 top-full mt-1 bg-[#211f1a] border border-[#514b3c] rounded-sm z-20 max-h-32 overflow-y-auto">
                                                <?php $__currentLoopData = $userCatalysts; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $catalyst): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                                    <div onclick="selectCatalystForEnhancement(<?php echo e($catalyst['id']); ?>, '<?php echo e($catalyst['name']); ?>', '<?php echo e($catalyst['rarity_name']); ?>', '<?php echo e(asset($catalyst['image_path'] ?? 'assets/icons/catalysts/default.png')); ?>')"
                                                        class="flex items-center p-1 hover:bg-[#2a2722] cursor-pointer border-b border-[#3d3a2e] last:border-b-0">
                                                        <img src="<?php echo e(asset($catalyst['image_path'] ?? 'assets/icons/catalysts/default.png')); ?>"
                                                            alt="<?php echo e($catalyst['name']); ?>" class="w-6 h-6 mr-1">
                                                        <div class="flex flex-col">
                                                            <span
                                                                class="text-[10px] text-[#d3c6a6]"><?php echo e($catalyst['name']); ?></span>
                                                            <span
                                                                class="text-[8px] text-[#9a9483]"><?php echo e($catalyst['rarity_name']); ?>

                                                                (<?php echo e($catalyst['quantity']); ?>)</span>
                                                        </div>
                                                    </div>
                                                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                            </div>
                                        <?php else: ?>
                                            <div class="text-[#9a9483] text-[10px] text-center px-2">
                                                Нет доступных катализаторов
                                            </div>
                                        <?php endif; ?>
                                    </div>
                                </div>
                            </div>
                        </div>

                        
                        <button id="enhance-potion-btn" onclick="enhancePotionWithSelectedItems()"
                            class="w-full py-1.5 text-sm font-medium text-center bg-gradient-to-b from-[#4a452c] to-[#333024] text-[#e5b769] border border-[#8c784e] rounded-md disabled:opacity-50 disabled:cursor-not-allowed transition-all duration-300 hover:from-[#5a5436] hover:to-[#3f3c2c] hover:border-[#a6925e]"
                            disabled>
                            Улучшить зелье
                        </button>

                        
                        <input type="hidden" id="selected-potion-id" value="">
                        <input type="hidden" id="selected-catalyst-id" value="">
                    </div>
                </div>

                
                <div>
                    <div class="flex justify-between items-center mb-2">
                        <h3 class="text-[#e5b769] font-semibold text-base border-b border-[#3d3a2e] pb-1">Журнал
                            алхимика
                        </h3>
                    </div>

                    <div class="bg-gradient-to-b from-[#2a2722] to-[#1d1916] border border-[#514b3c] rounded-md p-2">
                        <div id="alchemy-logs" class="space-y-1 max-h-24 sm:max-h-32 overflow-y-auto text-xs">
                            <div class="text-center py-2 text-[#9a9483]">
                                Журнал пуст
                            </div>
                        </div>
                    </div>
                </div>

                
                <script>
                    // Глобальная переменная для хранения выбранного рецепта
                    let selectedRecipeId = null;
                    let selectedRecipeData = null;

                    // Функции для работы с localStorage
                    function saveSelectedRecipe(recipeId) {
                        if (recipeId) {
                            localStorage.setItem('alchemist_selected_recipe', recipeId);
                            console.log('Сохранен выбранный рецепт в localStorage:', recipeId);
                        } else {
                            localStorage.removeItem('alchemist_selected_recipe');
                            console.log('Удален выбранный рецепт из localStorage');
                        }
                    }

                    function loadSelectedRecipe() {
                        const savedRecipeId = localStorage.getItem('alchemist_selected_recipe');
                        if (savedRecipeId) {
                            console.log('Загружен выбранный рецепт из localStorage:', savedRecipeId);
                            return savedRecipeId;
                        }
                        return null;
                    }

                    // Функция для выбора рецепта из списка и помещения его на стол
                    function selectPotion(potionId) {
                        console.log('Выбран рецепт с ID:', potionId);

                        // Выделяем визуально выбранное зелье
                        const potionElements = document.querySelectorAll('.potion-list .recipe-card');
                        potionElements.forEach(el => {
                            el.classList.remove('border-[#e5b769]');
                            el.classList.add('border-[#514b3c]');
                        });

                        const selectedElement = document.querySelector(`.potion-list [data-recipe-id="${potionId}"]`);
                        if (selectedElement) {
                            console.log('Найден элемент рецепта в DOM');
                            selectedElement.classList.remove('border-[#514b3c]');
                            selectedElement.classList.add('border-[#e5b769]');

                            // Сохраняем ID выбранного рецепта
                            selectedRecipeId = potionId;

                            // Сохраняем выбранный рецепт в localStorage
                            saveSelectedRecipe(potionId);

                            // Показываем индикатор загрузки
                            showRecipeLoading(true);

                            // Отправляем запрос на сервер для проверки ингредиентов
                            checkRecipeIngredients(potionId);
                        } else {
                            console.error('Элемент рецепта не найден в DOM');
                        }
                    }

                    // Функция для проверки ингредиентов для выбранного рецепта
                    function checkRecipeIngredients(recipeId) {
                        console.log('Проверка ингредиентов для рецепта с ID:', recipeId);

                        // Получаем CSRF-токен
                        const csrfToken = document.querySelector('meta[name="csrf-token"]').content;
                        console.log('CSRF-токен:', csrfToken ? 'Получен' : 'Не найден');

                        // Отправляем запрос на сервер
                        fetch('/masters/alchemist/check-ingredients', {
                            method: 'POST',
                            headers: {
                                'Content-Type': 'application/json',
                                'X-CSRF-TOKEN': csrfToken,
                                'Accept': 'application/json'
                            },
                            body: JSON.stringify({
                                recipe_id: recipeId
                            })
                        })
                            .then(response => {
                                console.log('Получен ответ от сервера:', response.status, response.statusText);
                                if (!response.ok) {
                                    // Если статус не 200-299, то сначала читаем текст ответа и потом вызываем ошибку
                                    return response.text().then(text => {
                                        // Пробуем разобрать как JSON, если не получается, вернем текст
                                        try {
                                            return { error: JSON.parse(text) };
                                        } catch (e) {
                                            // Выводим первые 100 символов ответа для диагностики
                                            const preview = text.substring(0, 100);
                                            throw new Error(`Ошибка ${response.status}: ${preview}...`);
                                        }
                                    });
                                }
                                return response.json();
                            })
                            .then(data => {
                                console.log('Данные от сервера:', data);

                                // Скрываем индикатор загрузки
                                showRecipeLoading(false);

                                // Проверяем, не получили ли мы объект с ошибкой
                                if (data.error) {
                                    console.error('Ошибка от сервера:', data.error);
                                    showErrorMessage(data.error.message || 'Произошла ошибка при проверке ингредиентов');
                                    clearRecipeSlot();
                                    return;
                                }

                                // Логируем информацию об ингредиентах для отладки
                                if (data.ingredients) {
                                    console.log('Ингредиенты:', data.ingredients);
                                    data.ingredients.forEach(ingredient => {
                                        console.log(`Ингредиент ${ingredient.name}: ${ingredient.user_quantity}/${ingredient.required_quantity} (${ingredient.has_enough ? 'достаточно' : 'недостаточно'})`);
                                    });
                                }

                                if (data.success) {
                                    console.log('Успешно получены данные о рецепте');

                                    // Сохраняем данные о рецепте
                                    selectedRecipeData = {
                                        id: data.recipe.id,
                                        name: data.recipe.name,
                                        description: data.recipe.description,
                                        iconSrc: data.recipe.icon_path,
                                        canBrew: data.can_brew,
                                        message: data.message,
                                        ingredients: data.ingredients,
                                        brewingTime: data.recipe.brewing_time_formatted,
                                        quantity: data.recipe.quantity || 1 // Добавляем количество рецептов
                                    };

                                    // Обновляем слот для рецепта
                                    updateRecipeSlot(true);
                                } else {
                                    console.error('Ошибка от сервера:', data.message);

                                    // Показываем сообщение об ошибке
                                    showErrorMessage(data.message || 'Не удалось проверить ингредиенты');
                                    clearRecipeSlot();
                                }
                            })
                            .catch(error => {
                                console.error('Ошибка при запросе:', error);
                                showRecipeLoading(false);

                                // Показываем детальные данные об ошибке
                                let errorMessage = 'Произошла ошибка при проверке ингредиентов';
                                if (error.message) {
                                    errorMessage += ': ' + error.message;
                                }

                                showErrorMessage(errorMessage);
                                clearRecipeSlot();
                            });
                    }

                    // Функция для отображения/скрытия индикатора загрузки
                    function showRecipeLoading(show) {
                        const emptySlot = document.getElementById('empty-recipe-slot');
                        const selectedRecipeElement = document.getElementById('selected-recipe');
                        const loadingElement = document.getElementById('recipe-loading');

                        if (show) {
                            emptySlot.classList.add('hidden');
                            selectedRecipeElement.classList.add('hidden');
                            loadingElement.classList.remove('hidden');
                        } else {
                            loadingElement.classList.add('hidden');
                        }
                    }

                    // Функция для обновления слота рецепта
                    function updateRecipeSlot(hasRecipe) {
                        console.log('Обновление слота рецепта, hasRecipe:', hasRecipe);

                        const emptySlot = document.getElementById('empty-recipe-slot');
                        const selectedRecipeElement = document.getElementById('selected-recipe');
                        const recipeSlotStatus = document.getElementById('recipe-slot-status');

                        console.log('DOM элементы:', {
                            emptySlot: emptySlot ? 'Найден' : 'Не найден',
                            selectedRecipeElement: selectedRecipeElement ? 'Найден' : 'Не найден',
                            recipeSlotStatus: recipeSlotStatus ? 'Найден' : 'Не найден'
                        });

                        if (hasRecipe && selectedRecipeData) {
                            console.log('Отображаем выбранный рецепт:', selectedRecipeData.name);

                            // Скрываем пустой слот и показываем выбранный рецепт
                            emptySlot.classList.add('hidden');
                            selectedRecipeElement.classList.remove('hidden');

                            // Обновляем статус слота
                            recipeSlotStatus.textContent = 'Выбран';
                            recipeSlotStatus.className = 'text-[#7cfc00] text-xs px-2 py-0.5 bg-[#1a301a] rounded border border-[#397239]';

                            // Создаем HTML для ингредиентов
                            let ingredientsHtml = '';
                            if (selectedRecipeData.ingredients && selectedRecipeData.ingredients.length > 0) {
                                ingredientsHtml = `
                                <div class="mt-2 mb-2 bg-[#1a1814] p-2 rounded">
                                    <div class="text-[#d3c6a6] text-xs font-medium mb-1 border-b border-[#3d3a2e] pb-1">Требуемые ингредиенты:</div>
                                    <div class="max-h-48 overflow-y-auto">
                                        <div class="grid grid-cols-2 gap-3">
                            `;

                                selectedRecipeData.ingredients.forEach(ingredient => {
                                    const hasEnough = ingredient.has_enough;
                                    // Вычисляем процент заполнения прогресс-бара
                                    const percentAvailable = ingredient.required_quantity > 0
                                        ? Math.min(100, (ingredient.user_quantity / ingredient.required_quantity) * 100)
                                        : 0;

                                    ingredientsHtml += `
                                    <div class="ingredient-card bg-[#211f1a] rounded border border-[#514b3c] p-3 hover:border-[#8c784e] transition-colors text-center">
                                        <!-- Иконка сверху -->
                                        <div class="flex justify-center mb-2">
                                            <div class="ingredient-icon-container w-12 h-12 bg-[#1a1814] rounded border border-[#514b3c] flex items-center justify-center">
                                                <img src="${ingredient.icon_path}" alt="${ingredient.name}" class="w-10 h-10 ${!hasEnough ? 'opacity-70' : ''}">
                                            </div>
                                        </div>
                                        <!-- Количество -->
                                        <div class="ingredient-quantity text-xs mb-2">
                                            <span class="${hasEnough ? 'text-[#a6c37b]' : 'text-[#e77c7c]'} font-medium">
                                                ${ingredient.user_quantity}/${ingredient.required_quantity}
                                            </span>
                                        </div>
                                        <!-- Прогресс-бар -->
                                        <div class="ingredient-progress w-full bg-[#1a1814] h-2 rounded-full overflow-hidden">
                                            <div class="${hasEnough ? 'bg-gradient-to-r from-[#4a5a2c] to-[#a6c37b]' : 'bg-gradient-to-r from-[#5a2c2c] to-[#e77c7c]'} h-full rounded-full"
                                                style="width: ${percentAvailable}%"></div>
                                        </div>
                                    </div>
                                `;
                                });

                                ingredientsHtml += `
                                        </div>
                                    </div>
                                </div>
                            `;
                            }

                            // Заполняем содержимое слота с рецептом
                            selectedRecipeElement.innerHTML = `
                            <div class="relative">
                                
                                <button onclick="clearRecipeSlot()" class="absolute -top-1 -right-1 w-6 h-6 bg-[#301a1a] rounded-full border border-[#562e2e] flex items-center justify-center hover:bg-[#3d2222] hover:border-[#6e3a3a] transition-colors duration-300 z-10">
                                    <svg xmlns="http://www.w3.org/2000/svg" class="h-3.5 w-3.5 text-[#e77c7c]" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="3">
                                        <line x1="18" y1="6" x2="6" y2="18"></line>
                                        <line x1="6" y1="6" x2="18" y2="18"></line>
                                    </svg>
                                </button>

                                <div class="flex items-center mb-2">
                                    <div class="w-10 h-10 bg-[#1a1814] rounded border border-[#514b3c] flex items-center justify-center mr-2 relative">
                                        <img src="${selectedRecipeData.iconSrc}" alt="${selectedRecipeData.name}" class="w-8 h-8">
                                        <div class="absolute -bottom-1 -right-1 bg-[#211f1a] border border-[#e5b769] rounded-full px-1 text-[10px] text-[#e5b769] font-medium">
                                            x${selectedRecipeData.quantity || 1}
                                        </div>
                                    </div>
                                    <div class="flex-1">
                                        <div class="text-[#d3c6a6] text-xs font-medium">${selectedRecipeData.name}</div>
                                        <div class="text-[#9a9483] text-[10px]">${selectedRecipeData.description || ''}</div>
                                    </div>
                                </div>
                                ${ingredientsHtml}
                                <div class="flex justify-between items-center text-[10px] mb-2">
                                    <span class="text-[#9a9483]">Время приготовления:</span>
                                    <span class="text-[#d3c6a6]">${selectedRecipeData.brewingTime}</span>
                                </div>
                                <div class="mt-3">
                                    <button onclick="startBrewing(${selectedRecipeData.id})"
                                        class="w-full text-xs px-2 py-1.5 ${selectedRecipeData.canBrew
                                    ? 'bg-gradient-to-b from-[#4a452c] to-[#333024] text-[#e5b769] border border-[#8c784e] hover:from-[#5a5436] hover:to-[#3f3c2c]'
                                    : 'bg-gradient-to-b from-[#3d3a33] to-[#252520] text-[#9a9483] border border-[#514b3c] opacity-70 cursor-not-allowed'}
                                        rounded-sm transition-colors duration-300"
                                        ${!selectedRecipeData.canBrew ? 'disabled' : ''}>
                                        ${selectedRecipeData.canBrew ? 'Приготовить' : 'Недостаточно ингредиентов'}
                                    </button>
                                </div>
                            </div>
                        `;
                        } else {
                            // Показываем пустой слот и скрываем выбранный рецепт
                            emptySlot.classList.remove('hidden');
                            selectedRecipeElement.classList.add('hidden');

                            // Обновляем статус слота
                            recipeSlotStatus.textContent = 'Не выбран';
                            recipeSlotStatus.className = 'text-[#e77c7c] text-xs px-2 py-0.5 bg-[#301a1a] rounded border border-[#562e2e]';

                            // Очищаем данные о выбранном рецепте
                            selectedRecipeId = null;
                            selectedRecipeData = null;
                        }
                    }

                    // Функция для очистки слота рецепта
                    function clearRecipeSlot() {
                        // Снимаем выделение с рецепта в списке
                        const potionElements = document.querySelectorAll('.potion-list .recipe-card');
                        potionElements.forEach(el => {
                            el.classList.remove('border-[#e5b769]');
                            el.classList.add('border-[#514b3c]');
                        });

                        // Удаляем сохраненный рецепт из localStorage
                        saveSelectedRecipe(null);

                        // Обновляем слот рецепта
                        updateRecipeSlot(false);
                    }

                    // Функция для отмены варки или удаления готового зелья
                    function cancelBrewing(brewingId = null) {
                        let confirmMessage = 'Вы уверены, что хотите отменить варку? Все ингредиенты будут потеряны.';

                        // Если передан ID варки, значит это отмена сбора готового зелья
                        if (brewingId) {
                            confirmMessage = 'Вы уверены, что хотите удалить готовое зелье? Оно будет безвозвратно потеряно.';
                        }

                        if (confirm(confirmMessage)) {
                            // Подготавливаем данные для запроса
                            const requestData = {};
                            if (brewingId) {
                                requestData.brewing_id = brewingId;
                            }

                            // Отправляем запрос на сервер для отмены варки или удаления готового зелья
                            fetch('/masters/alchemist/cancel-brewing', {
                                method: 'POST',
                                headers: {
                                    'Content-Type': 'application/json',
                                    'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').content
                                },
                                body: JSON.stringify(requestData)
                            })
                                .then(response => response.json())
                                .then(data => {
                                    if (data.success) {
                                        // Если успешно, перезагружаем страницу
                                        window.location.reload();
                                    } else {
                                        // Если ошибка, показываем сообщение
                                        showErrorMessage(data.message || 'Не удалось выполнить операцию');
                                    }
                                })
                                .catch(error => {
                                    console.error('Ошибка:', error);
                                    showErrorMessage('Произошла ошибка при выполнении операции');
                                });
                        }
                    }

                    // Функция для начала варки зелья
                    function startBrewing(recipeId, catalystId = null) {
                        // Проверяем, что нет активной варки
                        const activeBrewingElement = document.querySelector('#active-brewing');
                        if (activeBrewingElement && activeBrewingElement.dataset.active === 'true') {
                            showErrorMessage('У вас уже идет процесс варки зелья');
                            return;
                        }

                        // Проверяем, что рецепт выбран
                        if (!recipeId) {
                            showErrorMessage('Сначала выберите рецепт из рюкзака');
                            return;
                        }

                        // Отображаем индикатор загрузки
                        const brewButton = document.querySelector('#selected-recipe button:first-child');
                        if (brewButton) {
                            brewButton.disabled = true;
                            brewButton.textContent = 'Начинаем варку...';
                            brewButton.classList.add('opacity-50');
                        }

                        // Подготавливаем данные для запроса
                        const requestData = {
                            recipe_id: recipeId
                        };

                        // Если указан катализатор, добавляем его в запрос
                        if (catalystId) {
                            requestData.catalyst_id = catalystId;
                        }

                        // Отправляем запрос на сервер для начала варки
                        fetch('/masters/alchemist/start-brewing', {
                            method: 'POST',
                            headers: {
                                'Content-Type': 'application/json',
                                'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').content
                            },
                            body: JSON.stringify(requestData)
                        })
                            .then(response => response.json())
                            .then(data => {
                                if (data.success) {
                                    // Обновляем страницу для отображения активной варки
                                    window.location.reload();
                                } else {
                                    // Восстанавливаем кнопку
                                    if (brewButton) {
                                        brewButton.textContent = 'Приготовить';
                                        brewButton.disabled = false;
                                        brewButton.classList.remove('opacity-50');
                                    }
                                    // Показываем сообщение об ошибке
                                    showErrorMessage(data.message || 'Не удалось начать варку');
                                }
                            })
                            .catch(error => {
                                console.error('Ошибка:', error);
                                // Восстанавливаем кнопку
                                if (brewButton) {
                                    brewButton.textContent = 'Приготовить';
                                    brewButton.disabled = false;
                                    brewButton.classList.remove('opacity-50');
                                }
                                showErrorMessage('Произошла ошибка при попытке начать варку');
                            });
                    }

                    // Функция для отображения ошибки
                    function showErrorMessage(message) {
                        const errorToast = document.createElement('div');
                        errorToast.className = 'fixed top-4 left-1/2 transform -translate-x-1/2 bg-[#301a1a] border border-[#962c2c] text-[#e77c7c] px-4 py-2 rounded-md z-50 text-sm shadow-lg';
                        errorToast.innerHTML = `<div class="flex items-center"><svg xmlns="http://www.w3.org/2000/svg" class="w-4 h-4 mr-2" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2"><circle cx="12" cy="12" r="10"></circle><line x1="12" y1="8" x2="12" y2="12"></line><line x1="12" y1="16" x2="12.01" y2="16"></line></svg>${message}</div>`;
                        document.body.appendChild(errorToast);

                        // Добавляем в лог алхимии
                        const logsContainer = document.getElementById('alchemy-logs');
                        if (logsContainer) {
                            // Создаем HTML для нового лога
                            const logHtml = `<div class="text-xs py-1 border-b border-[#3d3a2e] text-[#e77c7c]">${message} <span class="text-[#9a9483] text-[10px]">сейчас</span></div>`;

                            // Добавляем лог в начало контейнера
                            logsContainer.innerHTML = logHtml + logsContainer.innerHTML;
                        }

                        // Скрываем через 5 секунд
                        setTimeout(() => {
                            errorToast.style.opacity = '0';
                            errorToast.style.transition = 'opacity 0.5s';
                            setTimeout(() => errorToast.remove(), 500);
                        }, 5000);
                    }

                    // Функция для проверки статуса варки
                    function checkBrewingStatus() {
                        const activeBrewingElement = document.querySelector('#active-brewing');
                        console.log('Проверка статуса варки, статус котла:', {
                            active: activeBrewingElement ? activeBrewingElement.dataset.active : 'элемент не найден',
                            completed: activeBrewingElement ? activeBrewingElement.dataset.completed : 'элемент не найден'
                        });

                        // Проверяем, что котел активен и в нем нет готового зелья
                        if (activeBrewingElement && activeBrewingElement.dataset.active === 'true' && activeBrewingElement.dataset.completed !== 'true') {
                            console.log('Отправляем запрос на проверку статуса варки');
                            // Используем прямой URL для проверки статуса варки
                            fetch('/masters/alchemist/check-brewing-status')
                                .then(response => {
                                    console.log('Статус ответа проверки варки:', response.status);
                                    if (!response.ok) {
                                        throw new Error('Ошибка при проверке статуса варки: ' + response.status);
                                    }
                                    return response.json();
                                })
                                .then(data => {
                                    if (data.success) {
                                        // Обновляем прогресс и время
                                        const progressBar = activeBrewingElement.querySelector('.bg-gradient-to-r');
                                        if (progressBar) {
                                            progressBar.style.width = `${data.brewing_progress}%`;
                                        }

                                        const timeLeftElement = document.getElementById('brewing-time-left');
                                        if (timeLeftElement) {
                                            // Проверяем, завершена ли варка, и выбираем соответствующий текст
                                            if (data.completed) {
                                                timeLeftElement.textContent = 'Осталось: 0:00';
                                            } else {
                                                timeLeftElement.textContent = `Осталось: ${data.time_left_formatted}`;
                                            }
                                        }

                                        // Если варка завершена, обновляем интерфейс без перезагрузки страницы
                                        if (data.completed) {
                                            console.log('Варка завершена, обновляем интерфейс');

                                            // Обновляем атрибут data-completed, чтобы отметить, что зелье готово
                                            activeBrewingElement.dataset.completed = 'true';
                                            console.log('Атрибут data-completed обновлен:', activeBrewingElement.dataset.completed);

                                            // Обновляем статус варки в котле
                                            // Создаем форму для сбора зелья
                                            const collectForm = document.createElement('form');
                                            collectForm.setAttribute('action', '<?php echo e(route('masters.alchemist.collectPotionRedirect')); ?>');
                                            collectForm.setAttribute('method', 'POST');
                                            collectForm.className = 'mt-2';

                                            // Создаем CSRF-токен
                                            const csrfToken = document.createElement('input');
                                            csrfToken.setAttribute('type', 'hidden');
                                            csrfToken.setAttribute('name', '_token');
                                            csrfToken.setAttribute('value', document.querySelector('meta[name="csrf-token"]').content);

                                            // Создаем скрытое поле с ID варки
                                            const brewingIdInput = document.createElement('input');
                                            brewingIdInput.setAttribute('type', 'hidden');
                                            brewingIdInput.setAttribute('name', 'brewing_id');
                                            brewingIdInput.setAttribute('value', data.brewing_id);

                                            // Создаем кнопку отправки формы
                                            const submitButton = document.createElement('button');
                                            submitButton.setAttribute('type', 'submit');
                                            submitButton.className = 'w-full text-xs px-2 py-1.5 bg-gradient-to-b from-[#4a5a2c] to-[#334024] text-[#a6e269] border border-[#778c4e] rounded-sm hover:from-[#5a6a36] hover:to-[#3f4c2c] transition-colors duration-300';
                                            submitButton.textContent = 'Собрать готовое зелье';

                                            // Добавляем все элементы в форму
                                            collectForm.appendChild(csrfToken);
                                            collectForm.appendChild(brewingIdInput);
                                            collectForm.appendChild(submitButton);

                                            // Заменяем кнопку отмены на форму сбора
                                            const cancelButton = activeBrewingElement.querySelector('button');
                                            if (cancelButton) {
                                                cancelButton.replaceWith(collectForm);
                                            }

                                            // Добавляем класс "completed" к котлу
                                            activeBrewingElement.classList.add('completed');

                                            // Обновляем статус в заголовке котла
                                            const statusSpan = activeBrewingElement.querySelector('.flex.items-center.justify-between.mb-2 span:last-child');
                                            if (statusSpan) {
                                                statusSpan.textContent = 'Готово';
                                                statusSpan.className = 'text-[#a6e269] text-xs px-2 py-0.5 bg-[#1a301a] rounded border border-[#397239]';
                                            }

                                            // Обновляем содержимое котла, чтобы показать готовое зелье
                                            const brewingContent = activeBrewingElement.querySelector('.flex.flex-col');
                                            if (brewingContent) {
                                                // Определяем цвет свечения в зависимости от качества зелья
                                                let qualityColor = 'gold';
                                                switch (data.potion_quality) {
                                                    case 'Обычное': qualityColor = 'green'; break;
                                                    case 'Необычное': qualityColor = 'blue'; break;
                                                    case 'Редкое': qualityColor = 'purple'; break;
                                                    case 'Эпическое': qualityColor = 'red'; break;
                                                    case 'Легендарное': qualityColor = 'gold'; break;
                                                }

                                                // Обновляем иконку и название зелья в котле
                                                const potionIcon = brewingContent.querySelector('.w-10.h-10 img');
                                                if (potionIcon) {
                                                    // Логируем путь к иконке для отладки
                                                    console.log('Обновляем иконку зелья:', data.potion_icon_path);

                                                    // Устанавливаем путь к иконке и обновляем атрибуты
                                                    potionIcon.src = data.potion_icon_path || '<?php echo e(asset('assets/potions/smallBottleHP.png')); ?>';
                                                    potionIcon.alt = data.potion_name;
                                                    potionIcon.classList.add('animate-pulse');

                                                    // Принудительно обновляем изображение
                                                    potionIcon.onload = function () {
                                                        console.log('Иконка зелья загружена успешно');
                                                    };

                                                    potionIcon.onerror = function () {
                                                        console.error('Ошибка загрузки иконки зелья:', this.src);
                                                        // Пробуем использовать запасную иконку
                                                        this.src = '<?php echo e(asset('assets/potions/smallBottleHP.png')); ?>';
                                                    };

                                                    // Добавляем свечение в зависимости от качества
                                                    const iconContainer = potionIcon.closest('.w-10.h-10');
                                                    if (iconContainer) {
                                                        // Удаляем все предыдущие классы свечения
                                                        iconContainer.classList.remove('glow-green', 'glow-blue', 'glow-purple', 'glow-red', 'glow-gold');
                                                        // Добавляем новый класс свечения
                                                        iconContainer.classList.add(`glow-${qualityColor}`);
                                                    }
                                                }

                                                // Обновляем название и описание зелья
                                                // Используем более надежный селектор без квадратных скобок с #
                                                const potionName = brewingContent.querySelector('.text-xs.font-medium');
                                                if (potionName) {
                                                    potionName.textContent = data.potion_name + ' (готово)';
                                                    potionName.classList.add('text-[#a6e269]'); // Зеленый цвет для готового зелья
                                                }
                                            }

                                            // Очищаем слот рецепта, так как рецепт уже использован
                                            const recipeSlotStatus = document.getElementById('recipe-slot-status');
                                            if (recipeSlotStatus) {
                                                recipeSlotStatus.textContent = 'Не выбран';
                                                recipeSlotStatus.className = 'text-[#e77c7c] text-xs px-2 py-0.5 bg-[#301a1a] rounded border border-[#562e2e]';
                                            }

                                            // Очищаем содержимое слота рецепта
                                            const recipeSlotContent = document.getElementById('recipe-slot-content');
                                            if (recipeSlotContent) {
                                                // Показываем пустой слот и скрываем выбранный рецепт
                                                const emptySlot = document.getElementById('empty-recipe-slot');
                                                const selectedRecipe = document.getElementById('selected-recipe');
                                                if (emptySlot) emptySlot.classList.remove('hidden');
                                                if (selectedRecipe) selectedRecipe.classList.add('hidden');
                                            }

                                            // Добавляем запись в журнал алхимии
                                            const logsContainer = document.getElementById('alchemy-logs');
                                            if (logsContainer) {
                                                const logHtml = `<div class="text-xs py-1 border-b border-[#3d3a2e] text-[#a6c37b]">Зелье "${data.potion_name}" готово к сбору <span class="text-[#9a9483] text-[10px]">только что</span></div>`;
                                                logsContainer.innerHTML = logHtml + logsContainer.innerHTML;
                                            }
                                        }
                                    }
                                })
                                .catch(error => {
                                    console.error('Ошибка при проверке статуса:', error);

                                    // Добавляем счетчик повторных попыток
                                    if (!window.brewingRetryCount) {
                                        window.brewingRetryCount = 0;
                                    }

                                    window.brewingRetryCount++;

                                    if (window.brewingRetryCount <= 3) {
                                        // Пробуем снова через 5 секунд, но не показываем ошибку пользователю
                                        console.log(`Повторная попытка ${window.brewingRetryCount}/3 через 5 секунд`);
                                        setTimeout(checkBrewingStatus, 5000);
                                    } else {
                                        // Если превышено максимальное количество попыток, показываем ошибку
                                        showErrorMessage('Ошибка при проверке статуса варки. Пожалуйста, обновите страницу.');

                                        // Сбрасываем счетчик для следующей серии проверок
                                        window.brewingRetryCount = 0;
                                    }
                                });
                        }
                    }

                    // Функция для сбора готового                  зелья через AJAX (не используется, заменена на серверную обра                                          бо                                        тку)
                    /*
                    function collectPotion(brewingId) {
                        console.log('Сбор зелья, ID варки:', brewingId);
        
                        // Проверяем, что ID варки не пустой
                        if (!brewingId) {
                            console.error('ID варки не указан');
                            showErrorMessage('Ошибка: ID варки не указан');
                            return;
                        }
        
                        // Преобразуем ID в строку, если это не строка
                        const brewingIdStr = String(brewingId);
                        console.log('ID варки (строка):', brewingIdStr);
        
                        // Отключаем кнопку сбора зелья, чтобы избежать повторных нажатий
                        const collectButton = document.querySelector('#active-brewing button');
                        if (collectButton) {
                            collectButton.disabled = true;
                            collectButton.textContent = 'Собираем зелье...';
                            collectButton.classList.add('opacity-50');
                        }
        
                        fetch('/masters/alchemist/collect-potion', {
                            method: 'POST',
                            headers: {
                                'Content-Type': 'application/json',
                                'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').content,
                                'Accept': 'application/json'
                            },
                            body: JSON.stringify({
                                unique_id: brewingIdStr
                            })
                        })
                            .then(response => {
                                console.log('Статус ответа:', response.status);
                                if (!response.ok) {
                                    throw new Error('Ошибка при сборе зелья: ' + response.status);
                                }
                                return response.json().catch(error => {
                                    console.error('Ошибка при разборе JSON:', error);
                                    throw new Error('Ошибка при разборе ответа сервера');
                                });
                            })
                            .then(data => {
                                console.log('Ответ сервера:', data);
                                if (data.success) {
                                    // Вместо перезагрузки страницы, обновляем интерфейс
                                    // Очищаем котел
                                    const activeBrewingElement = document.querySelector('#active-brewing');
                                    if (activeBrewingElement) {
                                        activeBrewingElement.dataset.active = 'false';
                                        activeBrewingElement.innerHTML = `
                                            <div class="flex items-center justify-between mb-2">
                                                <span class="text-[#d3c6a6] text-sm font-medium">Котёл</span>
                                                <span class="text-[#e77c7c] text-xs px-2 py-0.5 bg-[#301a1a] rounded border border-[#562e2e]">
                                                    Пуст
                                                </span>
                                            </div>
                                            <div class="text-center py-6 opacity-80">
                                                <div class="text-[#9a9483] text-sm mb-1">Котел пуст и готов к работе</div>
                                                <div class="text-[#d3c6a6] text-xs">Выберите рецепт из рюкзака и поместите его на стол</div>
                                            </div>
                                        `;
                                    }
        
                                    // Очищаем слот рецепта, так как рецепт уже использован
                                    const recipeSlot = document.querySelector('#recipe-slot');
                                    if (recipeSlot) {
                                        // Обновляем статус слота
                                        const recipeSlotStatus = document.getElementById('recipe-slot-status');
                                        if (recipeSlotStatus) {
                                            recipeSlotStatus.textContent = 'Не выбран';
                                            recipeSlotStatus.className = 'text-[#e77c7c] text-xs px-2 py-0.5 bg-[#301a1a] rounded border border-[#562e2e]';
                                        }
        
                                        // Очищаем содержимое слота рецепта
                                        const recipeSlotContent = document.getElementById('recipe-slot-content');
                                        if (recipeSlotContent) {
                                            // Показываем пустой слот и скрываем выбранный рецепт
                                            const emptySlot = document.getElementById('empty-recipe-slot');
                                            const selectedRecipe = document.getElementById('selected-recipe');
                                            if (emptySlot) emptySlot.classList.remove('hidden');
                                            if (selectedRecipe) selectedRecipe.classList.add('hidden');
                                        }
                                    }
        
                                    // Показываем сообщение об успехе
                                    const successMessage = `Зелье "${data.potion.name}" (${data.potion.quality}) успешно собрано! Получено ${data.experience_gained} опыта.`;
        
                                    // Создаем всплывающее уведомление
                                    const successToast = document.createElement('div');
                                    successToast.className = 'fixed top-4 left-1/2 transform -translate-x-1/2 bg-[#1a301a] border border-[#397239] text-[#7cfc00] px-4 py-2 rounded-md z-50 text-sm shadow-lg';
                                    successToast.innerHTML = `<div class="flex items-center"><svg xmlns="http://www.w3.org/2000/svg" class="w-4 h-4 mr-2" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2"><path d="M22 11.08V12a10 10 0 1 1-5.93-9.14"></path><polyline points="22 4 12 14.01 9 11.01"></polyline></svg>${successMessage}</div>`;
                                    document.body.appendChild(successToast);
        
                                    // Скрываем через 5 секунд
                                    setTimeout(() => {
                                        successToast.style.opacity = '0';
                                        successToast.style.transition = 'opacity 0.5s';
                                        setTimeout(() => successToast.remove(), 500);
                                    }, 5000);
        
                                    // Добавляем запись в журнал алхимии
                                    const logsContainer = document.getElementById('alchemy-logs');
                                    if (logsContainer) {
                                        // Создаем HTML для нового лога
                                        const logHtml = `<div class="text-xs py-1 border-b border-[#3d3a2e] text-[#a6c37b]">${successMessage} <span class="text-[#9a9483] text-[10px]">только что</span></div>`;
        
                                        // Добавляем лог в начало контейнера
                                        logsContainer.innerHTML = logHtml + logsContainer.innerHTML;
                                    }
                                } else {
                                    // Восстанавливаем кнопку
                                    if (collectButton) {
                                        collectButton.disabled = false;
                                        collectButton.textContent = 'Собрать готовое зелье';
                                        collectButton.classList.remove('opacity-50');
                                    }
                                    // Используем showErrorMessage вместо alert для единообразия
                                    showErrorMessage(data.message || 'Не удалось собрать зелье');
                                }
                            })
                            .catch(error => {
                                console.error('Ошибка:', error);
                                // Восстанавливаем кнопку
                                if (collectButton) {
                                    collectButton.disabled = false;
                                    collectButton.textContent = 'Собрать готовое зелье';
                                    collectButton.classList.remove('opacity-50');
                                }
                                showErrorMessage('Произошла ошибка при попытке собрать зелье');
                            });
                    }
                    */

                    function enhancePotion(potionId, catalystId) {
                        // Проверяем, что указаны ID зелья и катализатора
                        if (!potionId || !catalystId) {
                            showErrorMessage('Выберите зелье и катализатор для улучшения');
                            return;
                        }

                        // Отображаем индикатор загрузки
                        const enhanceButton = document.getElementById('enhance-potion-btn');
                        if (enhanceButton) {
                            const originalText = enhanceButton.textContent;
                            enhanceButton.disabled = true;
                            enhanceButton.textContent = 'Улучшаем...';
                            enhanceButton.classList.add('opacity-50');
                        }

                        // Отправляем запрос на сервер для улучшения зелья
                        fetch('/masters/alchemist/enhance-potion', {
                            method: 'POST',
                            headers: {
                                'Content-Type': 'application/json',
                                'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').content
                            },
                            body: JSON.stringify({
                                potion_id: potionId,
                                catalyst_id: catalystId
                            })
                        })
                            .then(response => response.json())
                            .then(data => {
                                if (data.success) {
                                    // Показываем сообщение об успехе
                                    const successToast = document.createElement('div');
                                    successToast.className = 'fixed top-4 left-1/2 transform -translate-x-1/2 bg-[#1a301a] border border-[#397239] text-[#7cfc00] px-4 py-2 rounded-md z-50 text-sm shadow-lg';
                                    successToast.innerHTML = `<div class="flex items-center"><svg xmlns="http://www.w3.org/2000/svg" class="w-4 h-4 mr-2" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2"><path d="M22 11.08V12a10 10 0 1 1-5.93-9.14"></path><polyline points="22 4 12 14.01 9 11.01"></polyline></svg>${data.message}</div>`;
                                    document.body.appendChild(successToast);

                                    // Скрываем через 5 секунд
                                    setTimeout(() => {
                                        successToast.style.opacity = '0';
                                        successToast.style.transition = 'opacity 0.5s';
                                        setTimeout(() => successToast.remove(), 500);
                                    }, 5000);

                                    // Обновляем страницу через 2 секунды
                                    setTimeout(() => {
                                        window.location.reload();
                                    }, 2000);
                                } else {
                                    // Восстанавливаем кнопку
                                    if (enhanceButton) {
                                        enhanceButton.textContent = originalText || 'Улучшить зелье';
                                        enhanceButton.disabled = false;
                                        enhanceButton.classList.remove('opacity-50');
                                    }
                                    // Показываем сообщение об ошибке
                                    showErrorMessage(data.message || 'Не удалось улучшить зелье');
                                }
                            })
                            .catch(error => {
                                console.error('Ошибка:', error);
                                // Восстанавливаем кнопку
                                if (enhanceButton) {
                                    enhanceButton.textContent = originalText || 'Улучшить зелье';
                                    enhanceButton.disabled = false;
                                    enhanceButton.classList.remove('opacity-50');
                                }
                                showErrorMessage('Произошла ошибка при попытке улучшить зелье');
                            });
                    }

                    function clearLogs() {
                        // Отправляем запрос на очистку логов
                        fetch('/masters/alchemist/clear-logs', {
                            method: 'POST',
                            headers: {
                                'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').content
                            }
                        })
                            .then(response => response.json())
                            .then(data => {
                                if (data.success) {
                                    // Очищаем журнал на странице
                                    const logsContainer = document.getElementById('alchemy-logs');
                                    if (logsContainer) {
                                        logsContainer.innerHTML = '<div class="text-center py-2 text-[#9a9483]">Журнал очищен</div>';
                                    }
                                } else {
                                    alert(data.message || 'Не удалось очистить журнал');
                                }
                            })
                            .catch(error => {
                                console.error('Ошибка:', error);
                                alert('Произошла ошибка при попытке очистить журнал');
                            });
                    }

                    // Функции для работы с улучшением зелий
                    let selectedPotionId = null;
                    let selectedCatalystId = null;

                    // Функция для отображения/скрытия выпадающего списка зелий
                    function togglePotionDropdown() {
                        const dropdown = document.getElementById('potion-dropdown');
                        if (dropdown) {
                            // Закрываем все открытые выпадающие списки
                            document.querySelectorAll('#catalyst-dropdown-enhancement, [id^="catalyst-dropdown-"]').forEach(el => {
                                el.classList.add('hidden');
                            });

                            // Переключаем видимость списка зелий
                            dropdown.classList.toggle('hidden');

                            // Добавляем обработчик клика вне списка для его закрытия
                            if (!dropdown.classList.contains('hidden')) {
                                const closeDropdown = (e) => {
                                    if (!dropdown.contains(e.target) && e.target.id !== 'selected-potion-display') {
                                        dropdown.classList.add('hidden');
                                        document.removeEventListener('click', closeDropdown);
                                    }
                                };

                                // Используем setTimeout, чтобы избежать срабатывания обработчика сразу
                                setTimeout(() => {
                                    document.addEventListener('click', closeDropdown);
                                }, 0);
                            }
                        }
                    }

                    // Функция для отображения/скрытия выпадающего списка катализаторов для улучшения
                    function toggleCatalystDropdownForEnhancement() {
                        const dropdown = document.getElementById('catalyst-dropdown-enhancement');
                        if (dropdown) {
                            // Закрываем все открытые выпадающие списки
                            document.querySelectorAll('#potion-dropdown, [id^="catalyst-dropdown-"]').forEach(el => {
                                if (el.id !== 'catalyst-dropdown-enhancement') {
                                    el.classList.add('hidden');
                                }
                            });

                            // Переключаем видимость списка катализаторов
                            dropdown.classList.toggle('hidden');

                            // Добавляем обработчик клика вне списка для его закрытия
                            if (!dropdown.classList.contains('hidden')) {
                                const closeDropdown = (e) => {
                                    if (!dropdown.contains(e.target) && e.target.id !== 'selected-catalyst-display') {
                                        dropdown.classList.add('hidden');
                                        document.removeEventListener('click', closeDropdown);
                                    }
                                };

                                // Используем setTimeout, чтобы избежать срабатывания обработчика сразу
                                setTimeout(() => {
                                    document.addEventListener('click', closeDropdown);
                                }, 0);
                            }
                        }
                    }

                    // Функция для выбора зелья для улучшения
                    function selectPotionForEnhancement(id, name, quality, iconPath) {
                        // Сохраняем ID выбранного зелья
                        selectedPotionId = id;
                        document.getElementById('selected-potion-id').value = id;

                        // Обновляем отображение выбранного зелья
                        const displayElement = document.getElementById('selected-potion-display');
                        if (displayElement) {
                            displayElement.innerHTML = `
                            <div class="flex items-center">
                                <img src="${iconPath}" alt="${name}" class="w-6 h-6 mr-1">
                                <div class="flex flex-col text-left">
                                    <span class="text-[10px] text-[#d3c6a6]">${name}</span>
                                    <span class="text-[8px] text-[#9a9483]">Качество: ${quality}</span>
                                </div>
                            </div>
                        `;
                        }

                        // Закрываем выпадающий список
                        const dropdown = document.getElementById('potion-dropdown');
                        if (dropdown) {
                            dropdown.classList.add('hidden');
                        }

                        // Проверяем, можно ли активировать кнопку улучшения
                        checkEnhanceButtonState();
                    }

                    // Функция для выбора катализатора для улучшения
                    function selectCatalystForEnhancement(id, name, rarity, iconPath) {
                        // Сохраняем ID выбранного катализатора
                        selectedCatalystId = id;
                        document.getElementById('selected-catalyst-id').value = id;

                        // Обновляем отображение выбранного катализатора
                        const displayElement = document.getElementById('selected-catalyst-display');
                        if (displayElement) {
                            displayElement.innerHTML = `
                            <div class="flex items-center">
                                <img src="${iconPath}" alt="${name}" class="w-6 h-6 mr-1">
                                <div class="flex flex-col text-left">
                                    <span class="text-[10px] text-[#d3c6a6]">${name}</span>
                                    <span class="text-[8px] text-[#9a9483]">${rarity}</span>
                                </div>
                            </div>
                        `;
                        }

                        // Закрываем выпадающий список
                        const dropdown = document.getElementById('catalyst-dropdown-enhancement');
                        if (dropdown) {
                            dropdown.classList.add('hidden');
                        }

                        // Проверяем, можно ли активировать кнопку улучшения
                        checkEnhanceButtonState();
                    }

                    // Функция для проверки состояния кнопки улучшения
                    function checkEnhanceButtonState() {
                        const enhanceButton = document.getElementById('enhance-potion-btn');
                        if (enhanceButton) {
                            enhanceButton.disabled = !(selectedPotionId && selectedCatalystId);
                        }
                    }

                    // Функция для улучшения зелья с выбранными предметами
                    function enhancePotionWithSelectedItems() {
                        const potionId = document.getElementById('selected-potion-id').value;
                        const catalystId = document.getElementById('selected-catalyst-id').value;

                        if (potionId && catalystId) {
                            enhancePotion(potionId, catalystId);
                        } else {
                            showErrorMessage('Выберите зелье и катализатор для улучшения');
                        }
                    }

                    // Функция для отображения/скрытия выпадающего списка катализаторов
                    function toggleCatalystDropdown(recipeId) {
                        const dropdown = document.getElementById(`catalyst-dropdown-${recipeId}`);
                        if (dropdown) {
                            // Закрываем все открытые выпадающие списки
                            document.querySelectorAll('[id^="catalyst-dropdown-"]').forEach(el => {
                                if (el.id !== `catalyst-dropdown-${recipeId}`) {
                                    el.classList.add('hidden');
                                }
                            });

                            // Переключаем видимость текущего списка
                            dropdown.classList.toggle('hidden');

                            // Добавляем обработчик клика вне списка для его закрытия
                            if (!dropdown.classList.contains('hidden')) {
                                const closeDropdown = (e) => {
                                    if (!dropdown.contains(e.target) && e.target.id !== `catalyst-dropdown-${recipeId}`) {
                                        dropdown.classList.add('hidden');
                                        document.removeEventListener('click', closeDropdown);
                                    }
                                };

                                // Используем setTimeout, чтобы избежать срабатывания обработчика сразу
                                setTimeout(() => {
                                    document.addEventListener('click', closeDropdown);
                                }, 0);
                            }
                        }
                    }

                    // Функция для отображения/скрытия подробной информации о рецепте
                    function toggleRecipeDetails(event, recipeId) {
                        // Предотвращаем всплытие события, чтобы не срабатывал клик по карточке
                        event.stopPropagation();

                        // Находим блок с подробной информацией
                        const detailsBlock = document.getElementById(`recipe-details-${recipeId}`);
                        if (detailsBlock) {
                            // Переключаем класс hidden для отображения/скрытия блока
                            detailsBlock.classList.toggle('hidden');

                            // Меняем текст кнопки в зависимости от состояния
                            const button = event.target;
                            if (detailsBlock.classList.contains('hidden')) {
                                button.textContent = 'Подробнее';
                            } else {
                                button.textContent = 'Скрыть';
                            }
                        }
                    }

                    // Функция-обработчик клика по карточке рецепта
                    function cardClickHandler() {
                        // Получаем ID рецепта из атрибута data-recipe-id
                        const recipeId = this.getAttribute('data-recipe-id');
                        console.log('Клик по карточке рецепта с ID:', recipeId);
                        selectPotion(recipeId);
                    }

                    // Инициализация при загрузке страницы
                    document.addEventListener('DOMContentLoaded', function () {
                        console.log('Алхимическая лаборатория загружена');

                        // Добавляем обработчики кликов для выбора рецептов
                        const recipeCards = document.querySelectorAll('.recipe-card');
                        console.log('Найдено карточек рецептов:', recipeCards.length);

                        recipeCards.forEach(card => {
                            console.log('Карточка рецепта:', card.getAttribute('data-recipe-id'));

                            // Удаляем существующий обработчик, если он есть
                            card.removeEventListener('click', cardClickHandler);

                            // Добавляем новый обработчик
                            card.addEventListener('click', cardClickHandler);
                        });

                        // Запускаем периодическую проверку статуса варки
                        const activeBrewingElement = document.querySelector('#active-brewing');
                        console.log('Статус котла при загрузке:', {
                            active: activeBrewingElement ? activeBrewingElement.dataset.active : 'элемент не найден',
                            completed: activeBrewingElement ? activeBrewingElement.dataset.completed : 'элемент не найден'
                        });

                        if (activeBrewingElement && activeBrewingElement.dataset.active === 'true' && activeBrewingElement.dataset.completed !== 'true') {
                            console.log('Запускаем проверку статуса варки');
                            // Проверяем статус каждые 5 секунд
                            setInterval(checkBrewingStatus, 5000);
                            // И сразу при загрузке страницы
                            checkBrewingStatus();
                        }

                        // Загружаем логи алхимии
                        updateAlchemyLogs();

                        // Загружаем сохраненный рецепт из localStorage
                        const savedRecipeId = loadSelectedRecipe();
                        if (savedRecipeId) {
                            // Проверяем, существует ли рецепт с таким ID на странице
                            const recipeElement = document.querySelector(`.recipe-card[data-recipe-id="${savedRecipeId}"]`);
                            if (recipeElement) {
                                console.log('Автоматически выбираем сохраненный рецепт:', savedRecipeId);
                                // Выбираем рецепт
                                selectPotion(savedRecipeId);
                            } else {
                                console.log('Сохраненный рецепт не найден на странице, очищаем localStorage');
                                saveSelectedRecipe(null);
                            }
                        }
                    });

                    // Функция для обновления логов алхимии
                    function updateAlchemyLogs() {
                        const logsContainer = document.getElementById('alchemy-logs');

                        // Если в контейнере нет логов, добавляем из серверных данных
                        if (logsContainer && logsContainer.childElementCount <= 1) {
                            <?php if(isset($alchemyLogs) && count($alchemyLogs) > 0): ?>
                                // Создаем HTML для логов
                                let logsHtml = '';
                                <?php $__currentLoopData = $alchemyLogs; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $log): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                    logsHtml += '<div class="text-xs py-1 border-b border-[#3d3a2e] <?php echo e($log->success ? "text-[#a6c37b]" : "text-[#e77c7c]"); ?>">';
                                    logsHtml += '<?php echo e($log->message); ?> <span class="text-[#9a9483] text-[10px]"><?php echo e($log->created_at); ?></span>';
                                    logsHtml += '</div>';
                                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                // Добавляем кнопку очистки логов
                                logsHtml += '<button class="w-full text-xs py-1 mt-2 bg-[#3d3a2e] text-[#9a9483] rounded hover:bg-[#514b3c] transition-colors" onclick="clearLogs()">Очистить журнал</button>';
                                // Обновляем содержимое контейнера
                                logsContainer.innerHTML = logsHtml;
                            <?php else: ?>
                                logsContainer.innerHTML = '<div class="text-center py-2 text-[#9a9483]">Журнал пуст</div>';
                            <?php endif; ?>
                    }
                    }
                </script>
            </div> 
            
            <?php if (isset($component)) { $__componentOriginal9e67914025cd6dd9a8c10d111e91463c = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginal9e67914025cd6dd9a8c10d111e91463c = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'components.layout.navigation-buttons','data' => []] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? $attributes->all() : [])); ?>
<?php $component->withName('layout.navigation-buttons'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag): ?>
<?php $attributes = $attributes->except(\Illuminate\View\AnonymousComponent::ignoredParameterNames()); ?>
<?php endif; ?>
<?php $component->withAttributes([]); ?>
<?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginal9e67914025cd6dd9a8c10d111e91463c)): ?>
<?php $attributes = $__attributesOriginal9e67914025cd6dd9a8c10d111e91463c; ?>
<?php unset($__attributesOriginal9e67914025cd6dd9a8c10d111e91463c); ?>
<?php endif; ?>
<?php if (isset($__componentOriginal9e67914025cd6dd9a8c10d111e91463c)): ?>
<?php $component = $__componentOriginal9e67914025cd6dd9a8c10d111e91463c; ?>
<?php unset($__componentOriginal9e67914025cd6dd9a8c10d111e91463c); ?>
<?php endif; ?>

            
            <?php if (isset($component)) { $__componentOriginal4766510e0268a7a5917e77b146281554 = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginal4766510e0268a7a5917e77b146281554 = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'components.layout.footer','data' => ['onlineCount' => $onlineCount]] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? $attributes->all() : [])); ?>
<?php $component->withName('layout.footer'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag): ?>
<?php $attributes = $attributes->except(\Illuminate\View\AnonymousComponent::ignoredParameterNames()); ?>
<?php endif; ?>
<?php $component->withAttributes(['onlineCount' => \Illuminate\View\Compilers\BladeCompiler::sanitizeComponentAttribute($onlineCount)]); ?>
<?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginal4766510e0268a7a5917e77b146281554)): ?>
<?php $attributes = $__attributesOriginal4766510e0268a7a5917e77b146281554; ?>
<?php unset($__attributesOriginal4766510e0268a7a5917e77b146281554); ?>
<?php endif; ?>
<?php if (isset($__componentOriginal4766510e0268a7a5917e77b146281554)): ?>
<?php $component = $__componentOriginal4766510e0268a7a5917e77b146281554; ?>
<?php unset($__componentOriginal4766510e0268a7a5917e77b146281554); ?>
<?php endif; ?>
        </div>
</body>

</html><?php /**PATH C:\Users\<USER>\Desktop\phpProject\resources\views/masters/alchemist.blade.php ENDPATH**/ ?>